#!/bin/bash

# UrbanEdge Email Service Deployment Script
# This script deploys the Supabase Edge Function for email handling

set -e

echo "🚀 UrbanEdge Email Service Deployment"
echo "======================================"

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI is not installed. Please install it first:"
    echo "   npm install -g supabase"
    echo "   or visit: https://supabase.com/docs/guides/cli"
    exit 1
fi

# Check if logged in to Supabase
if ! supabase projects list &> /dev/null; then
    echo "❌ Not logged in to Supabase. Please run:"
    echo "   supabase login"
    exit 1
fi

# Check if project is linked
if [ ! -f ".supabase/config.toml" ]; then
    echo "❌ Project not linked to Supabase. Please run:"
    echo "   supabase link --project-ref YOUR_PROJECT_REF"
    exit 1
fi

echo "✅ Prerequisites check passed"

# Check for required environment variables
echo ""
echo "🔧 Checking environment configuration..."

if [ -z "$WEB3FORMS_API_KEY" ]; then
    echo "⚠️  WEB3FORMS_API_KEY not found in environment"
    echo "   Please get your Web3Forms access key from: https://web3forms.com"
    echo "   Then set it as an environment variable:"
    echo "   export WEB3FORMS_API_KEY=your_web3forms_access_key"
    echo ""
    echo "   Or set it as a Supabase secret:"
    echo "   supabase secrets set WEB3FORMS_API_KEY=your_web3forms_access_key"
    echo ""
    read -p "Do you want to set the WEB3FORMS_API_KEY now? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        read -p "Enter your Web3Forms access key: " WEB3FORMS_KEY
        echo "Setting Web3Forms API key as Supabase secret..."
        supabase secrets set WEB3FORMS_API_KEY="$WEB3FORMS_KEY"
        echo "✅ Web3Forms API key set successfully"
    else
        echo "❌ Cannot proceed without Web3Forms API key"
        exit 1
    fi
else
    echo "✅ WEB3FORMS_API_KEY found in environment"
    echo "Setting as Supabase secret..."
    supabase secrets set WEB3FORMS_API_KEY="$WEB3FORMS_API_KEY"
fi

echo ""
echo "📦 Deploying Edge Function..."

# Deploy the Edge Function
if supabase functions deploy send-property-inquiry; then
    echo "✅ Edge Function deployed successfully!"
else
    echo "❌ Failed to deploy Edge Function"
    exit 1
fi

echo ""
echo "🧪 Testing the deployment..."

# Get project details
PROJECT_REF=$(grep 'project_id' .supabase/config.toml | cut -d'"' -f2)
FUNCTION_URL="https://$PROJECT_REF.supabase.co/functions/v1/send-property-inquiry"

echo "Function URL: $FUNCTION_URL"

# Test with a simple health check (OPTIONS request)
if curl -s -o /dev/null -w "%{http_code}" -X OPTIONS "$FUNCTION_URL" | grep -q "200"; then
    echo "✅ Function is responding to requests"
else
    echo "⚠️  Function may not be responding correctly"
fi

echo ""
echo "🎉 Deployment Complete!"
echo "======================"
echo ""
echo "Next steps:"
echo "1. Test the email functionality from your application"
echo "2. Monitor function logs: supabase functions logs send-property-inquiry --follow"
echo "3. Check Resend dashboard for email delivery status"
echo ""
echo "Function URL: $FUNCTION_URL"
echo "Documentation: docs/EMAIL_SERVICE_SETUP.md"
echo ""
echo "Happy emailing! 📧"
