# UrbanEdge Email Service API Documentation

## Overview

The UrbanEdge Email Service is a robust, production-ready email delivery system built on Supabase Edge Functions and Resend. It handles property inquiries and tour requests with comprehensive validation, rate limiting, and security features.

## API Endpoint

```
POST https://YOUR_PROJECT_REF.supabase.co/functions/v1/send-property-inquiry
```

## Authentication

Include your Supabase anon key in the Authorization header:

```
Authorization: Bearer YOUR_SUPABASE_ANON_KEY
```

## Request Format

### Headers

```
Content-Type: application/json
Authorization: Bearer YOUR_SUPABASE_ANON_KEY
```

### Request Body

#### Contact Inquiry

```json
{
  "propertyId": "uuid-string",
  "inquiryType": "contact",
  "clientName": "<PERSON>",
  "clientEmail": "<EMAIL>",
  "clientPhone": "************",
  "message": "I'm interested in this property. Please contact me."
}
```

#### Tour Request

```json
{
  "propertyId": "uuid-string",
  "inquiryType": "tour",
  "clientName": "<PERSON>",
  "clientEmail": "<EMAIL>",
  "clientPhone": "************",
  "message": "I'd like to schedule a tour.",
  "tourDate": "2024-02-15",
  "tourTime": "14:00"
}
```

### Field Validation

| Field | Type | Required | Validation |
|-------|------|----------|------------|
| `propertyId` | string | Yes | Valid UUID |
| `inquiryType` | string | Yes | "contact" or "tour" |
| `clientName` | string | Yes | 2-100 characters, letters/spaces only |
| `clientEmail` | string | Yes | Valid email format, max 254 chars |
| `clientPhone` | string | No | 7-20 chars, digits/spaces/dashes/parentheses |
| `message` | string | Yes | 10-2000 characters |
| `tourDate` | string | No* | ISO date format (YYYY-MM-DD) |
| `tourTime` | string | No* | Time format (HH:MM) |

*Required for tour requests

## Response Format

### Success Response (200)

```json
{
  "success": true,
  "message": "Inquiry sent successfully",
  "agentName": "John Smith"
}
```

### Error Responses

#### Validation Error (400)

```json
{
  "error": "Invalid inquiry data",
  "details": [
    "Valid client name is required (2-100 characters, letters only)",
    "Valid email address is required"
  ]
}
```

#### Property Not Found (404)

```json
{
  "error": "Property not found"
}
```

#### Rate Limit Exceeded (429)

```json
{
  "error": "Rate limit exceeded. Please try again later.",
  "resetTime": 1640995200000
}
```

#### Server Error (500)

```json
{
  "error": "Failed to send email. Please try again later."
}
```

## Rate Limiting

- **Limit**: 10 requests per 15 minutes per IP address
- **Headers**: Response includes rate limit information
  - `X-RateLimit-Remaining`: Requests remaining in current window
  - `X-RateLimit-Reset`: Unix timestamp when limit resets

## Security Features

### Input Validation
- All inputs are sanitized to prevent XSS attacks
- Email format validation with RFC compliance
- Phone number format validation
- Message length limits to prevent abuse

### Rate Limiting
- IP-based rate limiting prevents spam
- Configurable limits (default: 10 requests per 15 minutes)
- Automatic cleanup of expired rate limit entries

### Security Headers
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- `Referrer-Policy: strict-origin-when-cross-origin`

### Logging
- Security events are logged with client identification
- Failed attempts and rate limit violations are tracked
- Error logging for debugging and monitoring

## Email Templates

### Contact Inquiry Template
- Professional HTML design with property details
- Client information prominently displayed
- Direct reply and call action buttons
- Responsive design for mobile devices

### Tour Request Template
- Specialized layout for tour scheduling
- Highlighted tour date and time preferences
- Tour confirmation action buttons
- Professional branding and styling

## Error Handling

### Client-Side Integration

```javascript
try {
  const response = await supabase.functions.invoke('send-property-inquiry', {
    body: inquiryData,
  });

  if (response.error) {
    // Handle API errors
    console.error('API Error:', response.error);
    setError(response.error.message || 'Failed to send inquiry');
    return;
  }

  if (response.data.error) {
    // Handle business logic errors
    setError(response.data.error);
    return;
  }

  // Success
  setSuccess(`Inquiry sent to ${response.data.agentName}`);
} catch (error) {
  // Handle network errors
  console.error('Network Error:', error);
  setError('Network error. Please check your connection.');
}
```

### Common Error Scenarios

1. **Invalid Email Format**
   - Status: 400
   - Solution: Validate email on client-side before submission

2. **Rate Limit Exceeded**
   - Status: 429
   - Solution: Show user-friendly message with retry time

3. **Property Not Found**
   - Status: 404
   - Solution: Verify property exists and user has access

4. **Email Service Unavailable**
   - Status: 500
   - Solution: Show retry option and contact support

## Monitoring and Debugging

### Function Logs

```bash
# View real-time logs
supabase functions logs send-property-inquiry --follow

# View recent logs
supabase functions logs send-property-inquiry
```

### Resend Dashboard
- Monitor email delivery status
- Track bounce rates and spam reports
- View detailed delivery logs

### Security Event Monitoring

Security events are logged with the following types:
- `RATE_LIMIT_EXCEEDED`: IP exceeded request limits
- `INVALID_INPUT`: Malformed or invalid request data
- `EMAIL_SEND_FAILED`: Email service failure
- `UNEXPECTED_ERROR`: Unhandled server errors

## Testing

### Manual Testing

Use the provided test script:

```bash
# Set environment variables
export VITE_SUPABASE_URL=your_supabase_url
export VITE_SUPABASE_ANON_KEY=your_anon_key
export TEST_PROPERTY_ID=your_test_property_id

# Run tests
node scripts/test-email-service.js
```

### cURL Testing

```bash
curl -X POST 'https://YOUR_PROJECT_REF.supabase.co/functions/v1/send-property-inquiry' \
  -H 'Authorization: Bearer YOUR_ANON_KEY' \
  -H 'Content-Type: application/json' \
  -d '{
    "propertyId": "test-property-id",
    "inquiryType": "contact",
    "clientName": "Test User",
    "clientEmail": "<EMAIL>",
    "message": "Test inquiry message"
  }'
```

## Performance Considerations

### Response Times
- Typical response time: 200-500ms
- Email delivery: Asynchronous (immediate API response)
- Rate limiting: Minimal overhead (<10ms)

### Scalability
- Stateless design supports horizontal scaling
- Rate limiting uses in-memory storage (consider Redis for high traffic)
- Email service (Resend) handles delivery scaling

### Optimization Tips
1. Implement client-side validation to reduce API calls
2. Cache property data to minimize database queries
3. Use connection pooling for database connections
4. Monitor and optimize email template size

## Troubleshooting

### Common Issues

1. **"RESEND_API_KEY not configured"**
   - Ensure API key is set as Supabase secret
   - Verify key is valid and has send permissions

2. **"Property not found"**
   - Check property ID exists in database
   - Verify property has agent contact information

3. **Rate limit false positives**
   - Check if multiple users share same IP (corporate networks)
   - Consider implementing user-based rate limiting

4. **Email not delivered**
   - Check Resend dashboard for delivery status
   - Verify recipient email is valid
   - Check spam folders

### Support

For technical support:
1. Check function logs for error details
2. Verify Resend dashboard for email status
3. Review security event logs for suspicious activity
4. Contact development team with specific error messages
