// Security utilities for the property inquiry email service

export interface RateLimitEntry {
  count: number;
  resetTime: number;
  lastRequest: number;
}

export class RateLimiter {
  private storage = new Map<string, RateLimitEntry>();
  private windowMs: number;
  private maxRequests: number;
  private cleanupInterval: number;

  constructor(windowMs = 15 * 60 * 1000, maxRequests = 10) {
    this.windowMs = windowMs;
    this.maxRequests = maxRequests;
    this.cleanupInterval = setInterval(() => this.cleanup(), 5 * 60 * 1000); // Cleanup every 5 minutes
  }

  checkLimit(identifier: string): { allowed: boolean; remaining: number; resetTime: number } {
    const now = Date.now();
    const entry = this.storage.get(identifier);

    if (!entry || now > entry.resetTime) {
      // Create new entry or reset expired entry
      const newEntry: RateLimitEntry = {
        count: 1,
        resetTime: now + this.windowMs,
        lastRequest: now,
      };
      this.storage.set(identifier, newEntry);
      return {
        allowed: true,
        remaining: this.maxRequests - 1,
        resetTime: newEntry.resetTime,
      };
    }

    if (entry.count >= this.maxRequests) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: entry.resetTime,
      };
    }

    entry.count++;
    entry.lastRequest = now;
    return {
      allowed: true,
      remaining: this.maxRequests - entry.count,
      resetTime: entry.resetTime,
    };
  }

  private cleanup() {
    const now = Date.now();
    for (const [key, entry] of this.storage.entries()) {
      if (now > entry.resetTime + this.windowMs) {
        this.storage.delete(key);
      }
    }
  }

  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
  }
}

export function sanitizeInput(input: string): string {
  if (typeof input !== 'string') return '';
  
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .substring(0, 5000); // Limit length
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return emailRegex.test(email) && email.length <= 254;
}

export function validatePhone(phone: string): boolean {
  if (!phone) return true; // Phone is optional
  const phoneRegex = /^[\+]?[\d\s\-\(\)\.]{7,20}$/;
  return phoneRegex.test(phone);
}

export function validateName(name: string): boolean {
  return typeof name === 'string' && 
         name.trim().length >= 2 && 
         name.trim().length <= 100 &&
         /^[a-zA-Z\s\-\.\']+$/.test(name.trim());
}

export function validateMessage(message: string): boolean {
  return typeof message === 'string' && 
         message.trim().length >= 10 && 
         message.trim().length <= 2000;
}

export function validateDate(dateString: string): boolean {
  if (!dateString) return true; // Date is optional for tours
  const date = new Date(dateString);
  const now = new Date();
  const maxDate = new Date();
  maxDate.setFullYear(now.getFullYear() + 1); // Max 1 year in future
  
  return !isNaN(date.getTime()) && 
         date >= now && 
         date <= maxDate;
}

export function validateTime(timeString: string): boolean {
  if (!timeString) return true; // Time is optional for tours
  const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
  return timeRegex.test(timeString);
}

export function getClientIdentifier(request: Request): string {
  // Try to get real IP from various headers
  const forwardedFor = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');
  
  if (forwardedFor) {
    // x-forwarded-for can contain multiple IPs, take the first one
    return forwardedFor.split(',')[0].trim();
  }
  
  if (realIP) return realIP;
  if (cfConnectingIP) return cfConnectingIP;
  
  // Fallback to a combination of headers for identification
  const userAgent = request.headers.get('user-agent') || '';
  const acceptLanguage = request.headers.get('accept-language') || '';
  
  return `fallback-${btoa(userAgent + acceptLanguage).substring(0, 16)}`;
}

export function validateInquiryData(data: any): {
  isValid: boolean;
  errors: string[];
  sanitizedData?: any;
} {
  const errors: string[] = [];
  
  // Check required fields
  if (!data.propertyId || typeof data.propertyId !== 'string') {
    errors.push('Property ID is required');
  }
  
  if (!data.inquiryType || !['contact', 'tour'].includes(data.inquiryType)) {
    errors.push('Invalid inquiry type');
  }
  
  if (!validateName(data.clientName)) {
    errors.push('Valid client name is required (2-100 characters, letters only)');
  }
  
  if (!validateEmail(data.clientEmail)) {
    errors.push('Valid email address is required');
  }
  
  if (data.clientPhone && !validatePhone(data.clientPhone)) {
    errors.push('Invalid phone number format');
  }
  
  if (!validateMessage(data.message)) {
    errors.push('Message must be between 10-2000 characters');
  }
  
  // Tour-specific validations
  if (data.inquiryType === 'tour') {
    if (data.tourDate && !validateDate(data.tourDate)) {
      errors.push('Invalid tour date');
    }
    
    if (data.tourTime && !validateTime(data.tourTime)) {
      errors.push('Invalid tour time format (HH:MM)');
    }
  }
  
  if (errors.length > 0) {
    return { isValid: false, errors };
  }
  
  // Sanitize data
  const sanitizedData = {
    propertyId: data.propertyId.trim(),
    inquiryType: data.inquiryType,
    clientName: sanitizeInput(data.clientName),
    clientEmail: data.clientEmail.trim().toLowerCase(),
    clientPhone: data.clientPhone ? sanitizeInput(data.clientPhone) : undefined,
    message: sanitizeInput(data.message),
    tourDate: data.tourDate || undefined,
    tourTime: data.tourTime || undefined,
  };
  
  return { isValid: true, errors: [], sanitizedData };
}

export function createSecurityHeaders(): Record<string, string> {
  return {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Content-Security-Policy': "default-src 'none'",
  };
}

export function logSecurityEvent(event: string, details: any, request: Request) {
  const timestamp = new Date().toISOString();
  const clientIP = getClientIdentifier(request);
  const userAgent = request.headers.get('user-agent') || 'unknown';
  
  console.warn(`[SECURITY] ${timestamp} - ${event}`, {
    clientIP,
    userAgent,
    details,
  });
}
