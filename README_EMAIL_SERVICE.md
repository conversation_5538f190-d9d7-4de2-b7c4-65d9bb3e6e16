# UrbanEdge Email Service

A production-ready email delivery system for real estate property inquiries and tour requests, built with Supabase Edge Functions and Resend.

## 🚀 Features

### ✅ **Core Functionality**
- **Property Inquiries**: Handle contact requests for property information
- **Tour Scheduling**: Process tour booking requests with date/time preferences
- **Multi-Agent Support**: Route emails to specific property agents dynamically
- **Professional Templates**: Beautiful HTML email templates with responsive design

### 🔒 **Security & Reliability**
- **Rate Limiting**: 10 requests per 15 minutes per IP to prevent spam
- **Input Validation**: Comprehensive sanitization and validation of all inputs
- **Security Headers**: OWASP-compliant security headers on all responses
- **Error Handling**: Graceful error handling with user-friendly messages

### 📧 **Email Features**
- **HTML Templates**: Professional, branded email templates
- **Responsive Design**: Mobile-optimized email layouts
- **Direct Actions**: Reply and call buttons in emails
- **Delivery Tracking**: Integration with Resend for delivery monitoring

### 🛡️ **Production Ready**
- **Monitoring**: Comprehensive logging and security event tracking
- **Scalability**: Stateless design supports horizontal scaling
- **Testing**: Automated test suite and manual testing tools
- **Documentation**: Complete API documentation and setup guides

## 📋 **Quick Start**

### Prerequisites
- Supabase project with Edge Functions enabled
- Resend account for email delivery
- Node.js 16+ for testing scripts

### 1. Setup Resend
1. Sign up at [resend.com](https://resend.com)
2. Get your API key from the dashboard
3. (Optional) Configure custom domain

### 2. Deploy Edge Function
```bash
# Clone and navigate to project
cd UrbanEdge-3

# Set environment variables
export RESEND_API_KEY=your_resend_api_key

# Run deployment script
chmod +x scripts/deploy-email-service.sh
./scripts/deploy-email-service.sh
```

### 3. Test the Service
```bash
# Set test environment
export VITE_SUPABASE_URL=your_supabase_url
export VITE_SUPABASE_ANON_KEY=your_anon_key
export TEST_PROPERTY_ID=your_test_property_id

# Run test suite
node scripts/test-email-service.js
```

## 🏗️ **Architecture**

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   React App     │───▶│  Supabase Edge   │───▶│   Resend API    │
│ PropertyContact │    │    Function      │    │  Email Service  │
│     Form        │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │   Supabase DB    │
                       │ Property & Agent │
                       │      Data        │
                       └──────────────────┘
```

### Components

1. **PropertyContactForm**: React component with real-time validation
2. **Edge Function**: Serverless email processing with security
3. **Email Templates**: Professional HTML templates for different inquiry types
4. **Security Layer**: Rate limiting, validation, and monitoring
5. **Resend Integration**: Reliable email delivery service

## 📁 **File Structure**

```
├── supabase/functions/send-property-inquiry/
│   ├── index.ts              # Main Edge Function
│   ├── email-templates.ts    # HTML email templates
│   └── security.ts           # Security utilities
├── src/components/Properties/PropertyDetail/
│   └── PropertyContactForm.jsx  # React form component
├── scripts/
│   ├── deploy-email-service.sh  # Deployment script
│   └── test-email-service.js    # Test suite
└── docs/
    ├── EMAIL_SERVICE_SETUP.md   # Setup guide
    └── EMAIL_SERVICE_API.md     # API documentation
```

## 🔧 **Configuration**

### Environment Variables

#### Required
```env
RESEND_API_KEY=re_your_api_key_here
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_anon_key
```

#### Optional
```env
FROM_EMAIL=<EMAIL>
FROM_NAME=UrbanEdge Real Estate
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=10
```

### Supabase Secrets
```bash
supabase secrets set RESEND_API_KEY=your_key
supabase secrets set FROM_EMAIL=<EMAIL>
supabase secrets set FROM_NAME="UrbanEdge Real Estate"
```

## 📊 **API Usage**

### Send Property Inquiry
```javascript
const { data, error } = await supabase.functions.invoke('send-property-inquiry', {
  body: {
    propertyId: 'uuid-string',
    inquiryType: 'contact', // or 'tour'
    clientName: 'John Doe',
    clientEmail: '<EMAIL>',
    clientPhone: '************', // optional
    message: 'I am interested in this property.',
    // For tour requests:
    tourDate: '2024-02-15', // optional
    tourTime: '14:00'       // optional
  }
});
```

### Response
```javascript
// Success
{
  success: true,
  message: "Inquiry sent successfully",
  agentName: "Jane Smith"
}

// Error
{
  error: "Invalid inquiry data",
  details: ["Valid email address is required"]
}
```

## 🧪 **Testing**

### Automated Tests
```bash
# Run full test suite
node scripts/test-email-service.js

# Test specific scenarios
npm run test:email-valid
npm run test:email-invalid
npm run test:rate-limit
```

### Manual Testing
```bash
# Test contact inquiry
curl -X POST 'https://your-project.supabase.co/functions/v1/send-property-inquiry' \
  -H 'Authorization: Bearer your_anon_key' \
  -H 'Content-Type: application/json' \
  -d '{"propertyId":"test-id","inquiryType":"contact","clientName":"Test","clientEmail":"<EMAIL>","message":"Test message"}'
```

## 📈 **Monitoring**

### Function Logs
```bash
# Real-time logs
supabase functions logs send-property-inquiry --follow

# Recent logs
supabase functions logs send-property-inquiry
```

### Resend Dashboard
- Email delivery status
- Bounce and spam rates
- Delivery analytics

### Security Events
Monitor for:
- Rate limit violations
- Invalid input attempts
- Email delivery failures
- Unexpected errors

## 🔍 **Troubleshooting**

### Common Issues

**Email not sending**
```bash
# Check function logs
supabase functions logs send-property-inquiry

# Verify Resend API key
supabase secrets list | grep RESEND

# Test with minimal data
curl -X POST 'your-function-url' -d '{"propertyId":"test","inquiryType":"contact","clientName":"Test","clientEmail":"<EMAIL>","message":"Test"}'
```

**Rate limit issues**
- Check if multiple users share IP
- Monitor rate limit headers in responses
- Consider user-based rate limiting for high-traffic scenarios

**Property not found**
- Verify property exists in database
- Check property has agent contact information
- Ensure property ID format is correct

## 🚀 **Deployment**

### Production Checklist
- [ ] Resend API key configured
- [ ] Custom domain setup (optional)
- [ ] Edge Function deployed
- [ ] Test suite passing
- [ ] Monitoring configured
- [ ] Error handling tested
- [ ] Rate limiting verified
- [ ] Security headers confirmed

### Scaling Considerations
- **High Traffic**: Consider Redis for rate limiting
- **Multiple Regions**: Deploy Edge Functions globally
- **Email Volume**: Monitor Resend quotas and limits
- **Database**: Optimize property queries with indexes

## 📚 **Documentation**

- [Setup Guide](docs/EMAIL_SERVICE_SETUP.md) - Complete setup instructions
- [API Documentation](docs/EMAIL_SERVICE_API.md) - Detailed API reference
- [Security Guide](docs/SECURITY.md) - Security best practices
- [Troubleshooting](docs/TROUBLESHOOTING.md) - Common issues and solutions

## 🤝 **Contributing**

1. Fork the repository
2. Create feature branch (`git checkout -b feature/email-enhancement`)
3. Commit changes (`git commit -am 'Add email enhancement'`)
4. Push to branch (`git push origin feature/email-enhancement`)
5. Create Pull Request

## 📄 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 **Support**

- **Documentation**: Check docs/ directory
- **Issues**: Create GitHub issue with error details
- **Email**: Contact development team
- **Logs**: Include function logs and error messages

---

**Built with ❤️ for UrbanEdge Real Estate Platform**
