# UrbanEdge Email Service Troubleshooting Guide

This guide helps you diagnose and fix common issues with the UrbanEdge email service.

## 🚨 Quick Diagnostics

### Run Validation Script
```bash
npm run email:validate
```
This will check your entire setup and identify configuration issues.

### Check Function Logs
```bash
npm run email:logs
# or
supabase functions logs send-property-inquiry --follow
```

### Test Email Service
```bash
npm run email:test
```

## 🔧 Common Issues

### 1. "Function not found" (404 Error)

**Symptoms:**
- API returns 404 when calling the email function
- Error: "Could not find the function"

**Causes:**
- Edge Function not deployed
- Incorrect function URL
- Function deployment failed

**Solutions:**
```bash
# Check if function is deployed
supabase functions list

# Deploy the function
npm run email:deploy
# or
supabase functions deploy send-property-inquiry

# Verify deployment
curl -X OPTIONS 'https://YOUR_PROJECT.supabase.co/functions/v1/send-property-inquiry'
```

### 2. "RESEND_API_KEY not configured" Error

**Symptoms:**
- Function returns 500 error
- Logs show "RESEND_API_KEY not configured"

**Causes:**
- Resend API key not set as Supabase secret
- Invalid API key format

**Solutions:**
```bash
# Set the API key as Supabase secret
supabase secrets set RESEND_API_KEY=re_your_api_key_here

# Verify secrets are set
supabase secrets list

# Redeploy function after setting secrets
supabase functions deploy send-property-inquiry
```

### 3. "Property not found" Error

**Symptoms:**
- API returns 404 with "Property not found"
- Valid property ID provided

**Causes:**
- Property doesn't exist in database
- Property missing agent contact information
- Database migration not applied

**Solutions:**
```bash
# Check if property exists
curl -H "Authorization: Bearer YOUR_ANON_KEY" \
  "https://YOUR_PROJECT.supabase.co/rest/v1/properties?id=eq.PROPERTY_ID"

# Check if agent fields exist
curl -H "Authorization: Bearer YOUR_ANON_KEY" \
  "https://YOUR_PROJECT.supabase.co/rest/v1/properties?select=agent_name,agent_email&limit=1"

# If agent fields missing, run migrations
supabase db reset
# or apply specific migration
```

### 4. Rate Limit Issues

**Symptoms:**
- Users getting "Rate limit exceeded" frequently
- Legitimate users blocked

**Causes:**
- Multiple users sharing same IP (corporate networks)
- Rate limit too restrictive
- Bot attacks

**Solutions:**
```bash
# Check rate limit logs
supabase functions logs send-property-inquiry | grep "RATE_LIMIT"

# Adjust rate limits in security.ts
# Edit: supabase/functions/send-property-inquiry/security.ts
# Change: new RateLimiter(15 * 60 * 1000, 10) // 10 requests per 15 minutes

# Consider implementing user-based rate limiting
# Add user ID to rate limit key instead of just IP
```

### 5. Emails Not Being Delivered

**Symptoms:**
- Function returns success but emails not received
- No errors in logs

**Causes:**
- Invalid recipient email
- Emails going to spam
- Resend service issues
- Domain not verified

**Solutions:**
```bash
# Check Resend dashboard for delivery status
# Visit: https://resend.com/emails

# Test with known good email
curl -X POST 'https://YOUR_PROJECT.supabase.co/functions/v1/send-property-inquiry' \
  -H 'Authorization: Bearer YOUR_ANON_KEY' \
  -H 'Content-Type: application/json' \
  -d '{
    "propertyId": "test-id",
    "inquiryType": "contact",
    "clientName": "Test User",
    "clientEmail": "<EMAIL>",
    "message": "Test message"
  }'

# Check spam folders
# Verify domain in Resend dashboard
# Check Resend API status
```

### 6. CORS Errors

**Symptoms:**
- Browser console shows CORS errors
- Requests blocked by browser

**Causes:**
- Incorrect CORS configuration
- Missing preflight handling

**Solutions:**
```bash
# Check CORS headers in function response
curl -X OPTIONS 'https://YOUR_PROJECT.supabase.co/functions/v1/send-property-inquiry' \
  -H 'Origin: http://localhost:3000' \
  -v

# Verify CORS headers in index.ts:
# Access-Control-Allow-Origin: *
# Access-Control-Allow-Headers: authorization, x-client-info, apikey, content-type
```

### 7. Form Validation Errors

**Symptoms:**
- Form shows validation errors for valid data
- Inconsistent validation between client and server

**Causes:**
- Mismatched validation rules
- Client-side validation too strict/loose

**Solutions:**
```javascript
// Check validation in PropertyContactForm.jsx
// Ensure email regex matches server-side validation
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// Check phone validation
const phoneRegex = /^[\+]?[\d\s\-\(\)\.]{7,20}$/;

// Verify message length limits match server
// Client: 10-2000 characters
// Server: 10-2000 characters (in security.ts)
```

## 🔍 Debugging Steps

### 1. Enable Debug Logging

Add debug logs to Edge Function:
```typescript
// In index.ts, add before email sending:
console.log('Debug - Inquiry data:', JSON.stringify(inquiry, null, 2));
console.log('Debug - Property data:', JSON.stringify(property, null, 2));
```

### 2. Test Individual Components

**Test Database Connection:**
```bash
curl -H "Authorization: Bearer YOUR_ANON_KEY" \
  "https://YOUR_PROJECT.supabase.co/rest/v1/properties?limit=1"
```

**Test Edge Function Deployment:**
```bash
curl -X OPTIONS 'https://YOUR_PROJECT.supabase.co/functions/v1/send-property-inquiry'
```

**Test Resend API Directly:**
```bash
curl -X POST 'https://api.resend.com/emails' \
  -H 'Authorization: Bearer YOUR_RESEND_KEY' \
  -H 'Content-Type: application/json' \
  -d '{
    "from": "<EMAIL>",
    "to": "<EMAIL>",
    "subject": "Test",
    "text": "Test message"
  }'
```

### 3. Monitor Network Requests

In browser developer tools:
1. Open Network tab
2. Submit form
3. Check request/response details
4. Look for error status codes
5. Verify request payload

## 📊 Performance Issues

### Slow Response Times

**Symptoms:**
- Function takes >5 seconds to respond
- Timeouts in browser

**Causes:**
- Database query performance
- Email service latency
- Cold start delays

**Solutions:**
```bash
# Check function execution time in logs
supabase functions logs send-property-inquiry | grep "execution"

# Optimize database queries
# Add indexes to properties table
# Consider caching property data

# Monitor Resend API response times
# Check Resend status page
```

### High Memory Usage

**Symptoms:**
- Function crashes with memory errors
- Inconsistent performance

**Causes:**
- Large email templates
- Memory leaks in rate limiter
- Inefficient data processing

**Solutions:**
```typescript
// Optimize email templates (reduce size)
// Clean up rate limiter periodically
// Use streaming for large responses
```

## 🛡️ Security Issues

### Suspicious Activity

**Symptoms:**
- High rate of failed requests
- Unusual traffic patterns
- Security event logs

**Solutions:**
```bash
# Check security events
supabase functions logs send-property-inquiry | grep "SECURITY"

# Monitor rate limit violations
supabase functions logs send-property-inquiry | grep "RATE_LIMIT_EXCEEDED"

# Review IP addresses in logs
# Consider implementing IP blocking for repeat offenders
```

## 📞 Getting Help

### Information to Collect

When reporting issues, include:

1. **Error Messages:**
   ```bash
   # Function logs
   supabase functions logs send-property-inquiry --limit 50
   
   # Browser console errors
   # Network request details
   ```

2. **Environment Details:**
   ```bash
   # Supabase project ID
   # Function deployment status
   # Environment variables (without sensitive values)
   ```

3. **Reproduction Steps:**
   ```bash
   # Exact steps to reproduce
   # Sample data that causes the issue
   # Browser/device information
   ```

### Support Channels

1. **Documentation:** Check all docs/ files first
2. **Validation Script:** Run `npm run email:validate`
3. **Test Suite:** Run `npm run email:test`
4. **GitHub Issues:** Create issue with full details
5. **Development Team:** Contact with logs and reproduction steps

### Emergency Procedures

**If email service is completely down:**

1. **Check Supabase Status:** https://status.supabase.com
2. **Check Resend Status:** https://resend.com/status
3. **Verify Function Deployment:** `supabase functions list`
4. **Check Recent Changes:** Review recent deployments
5. **Rollback if Needed:** Deploy previous working version
6. **Enable Fallback:** Temporarily disable email features if critical

**Quick Rollback:**
```bash
# Deploy previous version
git checkout previous-working-commit
supabase functions deploy send-property-inquiry
git checkout main
```

Remember: Always test fixes in a development environment before applying to production!
