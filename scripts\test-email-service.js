#!/usr/bin/env node

/**
 * UrbanEdge Email Service Test Script
 *
 * This script tests the email service functionality by sending test inquiries
 * to the deployed Supabase Edge Function using FormSubmit.co for email delivery.
 */

const https = require("https");

// Configuration
const config = {
  supabaseUrl: process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL,
  supabaseKey:
    process.env.VITE_SUPABASE_ANON_KEY || process.env.SUPABASE_ANON_KEY,
  testPropertyId: process.env.TEST_PROPERTY_ID || "test-property-id",
};

// Validate configuration
if (!config.supabaseUrl || !config.supabaseKey) {
  console.error("❌ Missing required environment variables:");
  console.error("   VITE_SUPABASE_URL or SUPABASE_URL");
  console.error("   VITE_SUPABASE_ANON_KEY or SUPABASE_ANON_KEY");
  process.exit(1);
}

// Test data
const testInquiries = [
  {
    name: "Contact Inquiry Test",
    data: {
      propertyId: config.testPropertyId,
      inquiryType: "contact",
      clientName: "<PERSON> Do<PERSON>",
      clientEmail: "<EMAIL>",
      clientPhone: "************",
      message:
        "I am interested in this property. Please contact me with more information about the pricing and availability.",
    },
  },
  {
    name: "Tour Request Test",
    data: {
      propertyId: config.testPropertyId,
      inquiryType: "tour",
      clientName: "Jane Smith",
      clientEmail: "<EMAIL>",
      clientPhone: "************",
      message:
        "I would like to schedule a tour of this property. I am available on weekends.",
      tourDate: "2024-02-15",
      tourTime: "14:00",
    },
  },
  {
    name: "Minimal Contact Test",
    data: {
      propertyId: config.testPropertyId,
      inquiryType: "contact",
      clientName: "Bob Johnson",
      clientEmail: "<EMAIL>",
      message: "Quick question about this property.",
    },
  },
];

// Test invalid data
const invalidTests = [
  {
    name: "Invalid Email Test",
    data: {
      propertyId: config.testPropertyId,
      inquiryType: "contact",
      clientName: "Invalid User",
      clientEmail: "invalid-email",
      message: "This should fail due to invalid email.",
    },
    expectedStatus: 400,
  },
  {
    name: "Missing Required Fields Test",
    data: {
      propertyId: config.testPropertyId,
      inquiryType: "contact",
      clientName: "",
      clientEmail: "<EMAIL>",
      message: "",
    },
    expectedStatus: 400,
  },
];

// Helper function to make HTTP requests
function makeRequest(url, options, data) {
  return new Promise((resolve, reject) => {
    const req = https.request(url, options, (res) => {
      let body = "";
      res.on("data", (chunk) => (body += chunk));
      res.on("end", () => {
        try {
          const parsed = JSON.parse(body);
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: parsed,
          });
        } catch (e) {
          resolve({ status: res.statusCode, headers: res.headers, data: body });
        }
      });
    });

    req.on("error", reject);

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// Test function
async function testEmailService() {
  console.log("🧪 UrbanEdge Email Service Test Suite");
  console.log("=====================================\n");

  const functionUrl = `${config.supabaseUrl}/functions/v1/send-property-inquiry`;
  console.log(`Testing function: ${functionUrl}\n`);

  const headers = {
    "Content-Type": "application/json",
    Authorization: `Bearer ${config.supabaseKey}`,
  };

  let passedTests = 0;
  let totalTests = 0;

  // Test valid inquiries
  console.log("📧 Testing Valid Inquiries");
  console.log("---------------------------");

  for (const test of testInquiries) {
    totalTests++;
    console.log(`\n🔍 ${test.name}`);

    try {
      const response = await makeRequest(
        functionUrl,
        {
          method: "POST",
          headers,
        },
        test.data
      );

      if (response.status === 200 && response.data.success) {
        console.log(`✅ PASS - Email sent successfully`);
        console.log(`   Agent: ${response.data.agentName}`);
        console.log(
          `   Rate limit remaining: ${
            response.headers["x-ratelimit-remaining"] || "N/A"
          }`
        );
        passedTests++;
      } else {
        console.log(`❌ FAIL - Status: ${response.status}`);
        console.log(`   Response: ${JSON.stringify(response.data, null, 2)}`);
      }
    } catch (error) {
      console.log(`❌ FAIL - Error: ${error.message}`);
    }

    // Wait between requests to avoid rate limiting
    await new Promise((resolve) => setTimeout(resolve, 1000));
  }

  // Test invalid data
  console.log("\n\n🚫 Testing Invalid Data");
  console.log("------------------------");

  for (const test of invalidTests) {
    totalTests++;
    console.log(`\n🔍 ${test.name}`);

    try {
      const response = await makeRequest(
        functionUrl,
        {
          method: "POST",
          headers,
        },
        test.data
      );

      const expectedStatus = test.expectedStatus || 400;
      if (response.status === expectedStatus) {
        console.log(
          `✅ PASS - Correctly rejected with status ${response.status}`
        );
        console.log(
          `   Errors: ${JSON.stringify(
            response.data.details || response.data.error
          )}`
        );
        passedTests++;
      } else {
        console.log(
          `❌ FAIL - Expected status ${expectedStatus}, got ${response.status}`
        );
        console.log(`   Response: ${JSON.stringify(response.data, null, 2)}`);
      }
    } catch (error) {
      console.log(`❌ FAIL - Error: ${error.message}`);
    }

    await new Promise((resolve) => setTimeout(resolve, 1000));
  }

  // Test rate limiting
  console.log("\n\n⏱️  Testing Rate Limiting");
  console.log("-------------------------");

  totalTests++;
  console.log("\n🔍 Rate Limit Test (sending multiple requests quickly)");

  const rateLimitTest = {
    propertyId: config.testPropertyId,
    inquiryType: "contact",
    clientName: "Rate Limit Test",
    clientEmail: "<EMAIL>",
    message: "Testing rate limiting functionality.",
  };

  let rateLimitHit = false;
  for (let i = 0; i < 12; i++) {
    // Try to exceed the 10 request limit
    try {
      const response = await makeRequest(
        functionUrl,
        {
          method: "POST",
          headers,
        },
        rateLimitTest
      );

      if (response.status === 429) {
        console.log(`✅ Rate limit triggered after ${i + 1} requests`);
        rateLimitHit = true;
        break;
      }
    } catch (error) {
      // Continue testing
    }
  }

  if (rateLimitHit) {
    passedTests++;
    console.log("✅ PASS - Rate limiting is working correctly");
  } else {
    console.log("⚠️  WARNING - Rate limiting may not be working as expected");
  }

  // Summary
  console.log("\n\n📊 Test Results");
  console.log("===============");
  console.log(`Passed: ${passedTests}/${totalTests}`);
  console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);

  if (passedTests === totalTests) {
    console.log("\n🎉 All tests passed! Email service is working correctly.");
  } else {
    console.log(
      "\n⚠️  Some tests failed. Please check the configuration and logs."
    );
  }

  console.log("\n📝 Next Steps:");
  console.log("1. Check your email inbox for test messages");
  console.log(
    "2. If first email to an agent, they need to confirm their email address"
  );
  console.log("3. Check FormSubmit.co submission archive (API available)");
  console.log(
    "4. Monitor function logs: supabase functions logs send-property-inquiry --follow"
  );
}

// Run tests
if (require.main === module) {
  testEmailService().catch(console.error);
}

module.exports = { testEmailService };
