-- Add agent contact information fields to properties table

-- Add agent contact columns to properties table
ALTER TABLE properties
ADD COLUMN IF NOT EXISTS agent_name TEXT NOT NULL DEFAULT 'UrbanEdge Agent',
ADD COLUMN IF NOT EXISTS agent_email TEXT NOT NULL DEFAULT '<EMAIL>',
ADD COLUMN IF NOT EXISTS agent_phone TEXT,
ADD COLUMN IF NOT EXISTS agent_whatsapp_link TEXT;

-- Add email validation constraint
ALTER TABLE properties 
ADD CONSTRAINT valid_agent_email 
CHECK (agent_email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

-- Add phone validation constraint (basic format validation, optional field)
ALTER TABLE properties
ADD CONSTRAINT valid_agent_phone
CHECK (agent_phone IS NULL OR agent_phone ~* '^[\+]?[1-9][\d]{0,15}$|^\([0-9]{3}\)\s[0-9]{3}-[0-9]{4}$|^[0-9]{3}-[0-9]{3}-[0-9]{4}$');

-- Add WhatsApp link validation constraint (optional field, but if provided should be valid URL)
ALTER TABLE properties 
ADD CONSTRAINT valid_agent_whatsapp 
CHECK (agent_whatsapp_link IS NULL OR agent_whatsapp_link ~* '^https?://');

-- Create index for agent email for potential future queries
CREATE INDEX IF NOT EXISTS idx_properties_agent_email ON properties(agent_email);

-- Update existing properties with default agent information
-- This ensures existing properties have valid agent data
UPDATE properties
SET
  agent_name = 'UrbanEdge Agent',
  agent_email = '<EMAIL>'
WHERE agent_name IS NULL OR agent_email IS NULL;
