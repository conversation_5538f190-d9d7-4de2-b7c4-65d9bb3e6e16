# Migration Guide: From Resend to Web3Forms

This guide provides step-by-step instructions for migrating from Resend to Web3Forms for the UrbanEdge email service. Web3Forms is completely free and works with localhost and free hosting platforms without domain verification.

## Why Migrate to Web3Forms?

### Advantages of Web3Forms

✅ **Completely Free** - No monthly limits or paid tiers  
✅ **No Domain Verification** - Works with any domain including localhost  
✅ **Free Hosting Compatible** - Perfect for Vercel, Netlify, GitHub Pages  
✅ **Immediate Setup** - No complex configuration or waiting periods  
✅ **Unlimited Submissions** - No monthly email limits  
✅ **Multi-Agent Support** - Dynamic recipient emails work perfectly  

### Limitations of Resend

❌ **Paid Service** - Requires payment for production use  
❌ **Domain Verification** - Requires DNS configuration and domain ownership  
❌ **Complex Setup** - Multiple configuration steps and verification processes  
❌ **Free Hosting Issues** - Doesn't work well with free hosting platforms  

## Pre-Migration Checklist

Before starting the migration, ensure you have:

- [ ] Access to your Supabase project
- [ ] Supabase CLI installed and configured
- [ ] Current email service working (for comparison)
- [ ] Test property data in your database
- [ ] Backup of current configuration

## Step 1: Create Web3Forms Account

### 1.1 Sign Up for Web3Forms

1. **Visit**: [web3forms.com](https://web3forms.com)
2. **Sign Up**: Create a free account (no payment required)
3. **Verify Email**: Check your email and verify your account
4. **Login**: Access your Web3Forms dashboard

### 1.2 Create Your Form

1. **Create New Form**: Click "Create New Form" in dashboard
2. **Form Name**: Enter "UrbanEdge Property Inquiries"
3. **Copy Access Key**: Save the generated access key (format: `xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx`)

## Step 2: Update Environment Variables

### 2.1 Remove Resend Configuration

Remove or comment out Resend-related environment variables:

```bash
# Remove these from your .env files
# RESEND_API_KEY=re_your_resend_key
# FROM_EMAIL=<EMAIL>
# FROM_NAME=UrbanEdge Real Estate
```

### 2.2 Add Web3Forms Configuration

Add Web3Forms configuration to your `.env.local`:

```env
# Web3Forms Configuration
WEB3FORMS_API_KEY=your_web3forms_access_key
```

### 2.3 Update Supabase Secrets

Remove old secrets and add new ones:

```bash
# Remove Resend secrets (optional)
supabase secrets unset RESEND_API_KEY
supabase secrets unset FROM_EMAIL
supabase secrets unset FROM_NAME

# Add Web3Forms secret
supabase secrets set WEB3FORMS_API_KEY=your_web3forms_access_key
```

## Step 3: Update Edge Function Code

The Edge Function code has already been updated in this migration. The key changes are:

### 3.1 Updated sendEmail Function

The `sendEmail` function in `supabase/functions/send-property-inquiry/index.ts` now:

- Uses Web3Forms API instead of Resend API
- Sends form data instead of JSON
- Handles Web3Forms response format
- Maintains all existing functionality

### 3.2 Updated Environment Variable

Changed from `RESEND_API_KEY` to `WEB3FORMS_API_KEY`

### 3.3 Maintained Security Features

All security features remain intact:
- Rate limiting
- Input validation
- Error handling
- CORS configuration

## Step 4: Deploy Updated Function

### 4.1 Using Deployment Script

```bash
# Set environment variable
export WEB3FORMS_API_KEY=your_web3forms_access_key

# Run deployment script
./scripts/deploy-email-service.sh
```

### 4.2 Manual Deployment

```bash
# Set Supabase secret
supabase secrets set WEB3FORMS_API_KEY=your_web3forms_access_key

# Deploy function
supabase functions deploy send-property-inquiry

# Verify deployment
supabase functions list
```

## Step 5: Test the Migration

### 5.1 Run Validation Script

```bash
npm run email:validate
```

### 5.2 Run Test Suite

```bash
npm run email:test
```

### 5.3 Test Hosting Compatibility

```bash
node scripts/test-hosting-compatibility.js
```

### 5.4 Manual Testing

Test with a real property inquiry:

```bash
curl -X POST 'https://your-project.supabase.co/functions/v1/send-property-inquiry' \
  -H 'Authorization: Bearer your_supabase_anon_key' \
  -H 'Content-Type: application/json' \
  -d '{
    "propertyId": "real-property-id",
    "inquiryType": "contact",
    "clientName": "Test User",
    "clientEmail": "<EMAIL>",
    "message": "Migration test - please confirm receipt"
  }'
```

## Step 6: Update Documentation

### 6.1 Update Team Documentation

Inform your team about:
- New Web3Forms dashboard URL
- Updated environment variables
- New monitoring process

### 6.2 Update Deployment Processes

Update any CI/CD pipelines or deployment scripts to use:
- `WEB3FORMS_API_KEY` instead of `RESEND_API_KEY`
- Web3Forms dashboard for monitoring

## Step 7: Monitor and Verify

### 7.1 Monitor Web3Forms Dashboard

1. **Login**: Access your Web3Forms dashboard
2. **Check Submissions**: Verify emails are being received
3. **Monitor Status**: Ensure all submissions are successful

### 7.2 Monitor Supabase Logs

```bash
# Monitor function logs
supabase functions logs send-property-inquiry --follow

# Check for any errors
supabase functions logs send-property-inquiry | grep ERROR
```

### 7.3 Test User Experience

1. **Frontend Testing**: Test the contact form in your application
2. **Email Receipt**: Verify emails are received by property agents
3. **Error Handling**: Test with invalid data to ensure proper error messages

## Troubleshooting Migration Issues

### Issue 1: "WEB3FORMS_API_KEY not configured"

**Solution**:
```bash
# Verify secret is set
supabase secrets list | grep WEB3FORMS

# Set the secret if missing
supabase secrets set WEB3FORMS_API_KEY=your_access_key

# Redeploy function
supabase functions deploy send-property-inquiry
```

### Issue 2: Emails not being sent

**Possible Causes**:
- Invalid Web3Forms access key
- Network connectivity issues
- Invalid recipient email

**Solutions**:
1. Verify access key in Web3Forms dashboard
2. Check Supabase function logs
3. Test with a known good email address

### Issue 3: CORS errors

**Solution**: The CORS configuration remains the same and should work correctly.

### Issue 4: Rate limiting issues

**Solution**: Rate limiting configuration is unchanged and should work as before.

## Rollback Plan

If you need to rollback to Resend:

### 1. Restore Resend Configuration

```bash
# Restore Resend secrets
supabase secrets set RESEND_API_KEY=your_resend_key
supabase secrets set FROM_EMAIL=<EMAIL>
supabase secrets set FROM_NAME="UrbanEdge Real Estate"
```

### 2. Restore Previous Code

```bash
# Checkout previous version
git checkout previous-working-commit

# Deploy previous function
supabase functions deploy send-property-inquiry

# Return to current branch
git checkout main
```

## Post-Migration Benefits

After successful migration to Web3Forms:

### ✅ **Cost Savings**
- No monthly email service fees
- No domain verification costs
- No infrastructure maintenance

### ✅ **Simplified Development**
- Works with localhost immediately
- No domain verification delays
- Faster development cycles

### ✅ **Better Hosting Compatibility**
- Works with Vercel free tier
- Compatible with Netlify free tier
- Supports GitHub Pages
- No hosting platform restrictions

### ✅ **Improved Reliability**
- No monthly limits to worry about
- Professional email infrastructure
- Real-time dashboard monitoring

## Support and Resources

### Web3Forms Resources
- **Dashboard**: [web3forms.com/dashboard](https://web3forms.com/dashboard)
- **Documentation**: [docs.web3forms.com](https://docs.web3forms.com)
- **Support**: Contact Web3Forms support for service issues

### UrbanEdge Resources
- **Setup Guide**: `docs/WEB3FORMS_SETUP.md`
- **API Documentation**: `docs/EMAIL_SERVICE_API.md`
- **Troubleshooting**: `docs/TROUBLESHOOTING.md`

### Getting Help

If you encounter issues during migration:

1. **Check Logs**: Review Supabase function logs
2. **Run Tests**: Use the provided test scripts
3. **Verify Configuration**: Ensure all environment variables are set
4. **Contact Support**: Create GitHub issue with detailed error information

## Migration Checklist

Use this checklist to ensure complete migration:

- [ ] Web3Forms account created
- [ ] Access key obtained
- [ ] Environment variables updated
- [ ] Supabase secrets configured
- [ ] Edge Function deployed
- [ ] Tests passing
- [ ] Email delivery verified
- [ ] Dashboard monitoring setup
- [ ] Team documentation updated
- [ ] Rollback plan documented

## Conclusion

The migration from Resend to Web3Forms provides significant benefits for the UrbanEdge platform:

- **Zero Cost**: Completely free email service
- **Universal Compatibility**: Works with any hosting platform
- **Simplified Setup**: No domain verification required
- **Unlimited Usage**: No monthly submission limits

The migration maintains all existing functionality while providing better compatibility with free hosting platforms and development environments.
