// Email template utilities for property inquiries

export interface EmailTemplateData {
  inquiry: {
    propertyId: string;
    inquiryType: "contact" | "tour";
    clientName: string;
    clientEmail: string;
    clientPhone?: string;
    message: string;
    tourDate?: string;
    tourTime?: string;
  };
  property: {
    id: string;
    title: string;
    location: string;
    price: number;
    agent_name: string;
    agent_email: string;
    agent_phone?: string;
  };
}

export function generateContactInquiryTemplate(
  data: EmailTemplateData
): string {
  const { inquiry, property } = data;

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Property Inquiry - ${property.title}</title>
    </head>
    <body style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 0; background-color: #f5f5f5;">
      
      <!-- Header -->
      <div style="background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); color: white; padding: 30px 20px; text-align: center;">
        <h1 style="margin: 0; font-size: 28px; font-weight: 700;">UrbanEdge Real Estate</h1>
        <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">New Property Inquiry</p>
      </div>
      
      <!-- Main Content -->
      <div style="background-color: white; padding: 30px; margin: 0;">
        
        <!-- Property Header -->
        <div style="border-bottom: 2px solid #e5e7eb; padding-bottom: 20px; margin-bottom: 25px;">
          <h2 style="color: #1f2937; margin: 0 0 10px 0; font-size: 24px; font-weight: 600;">${
            property.title
          }</h2>
          <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;">
            <p style="margin: 0; color: #6b7280; font-size: 16px;">📍 ${
              property.location
            }</p>
            <p style="margin: 0; color: #059669; font-size: 20px; font-weight: 700;">$${property.price.toLocaleString()}</p>
          </div>
        </div>
        
        <!-- Client Information -->
        <div style="background-color: #f8fafc; padding: 20px; border-radius: 12px; margin: 25px 0; border-left: 4px solid #2563eb;">
          <h3 style="color: #374151; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">👤 Client Information</h3>
          <div style="display: grid; gap: 8px;">
            <p style="margin: 0; font-size: 15px;"><strong>Name:</strong> ${
              inquiry.clientName
            }</p>
            <p style="margin: 0; font-size: 15px;"><strong>Email:</strong> <a href="mailto:${
              inquiry.clientEmail
            }" style="color: #2563eb; text-decoration: none;">${
    inquiry.clientEmail
  }</a></p>
            ${
              inquiry.clientPhone
                ? `<p style="margin: 0; font-size: 15px;"><strong>Phone:</strong> <a href="tel:${inquiry.clientPhone}" style="color: #2563eb; text-decoration: none;">${inquiry.clientPhone}</a></p>`
                : ""
            }
          </div>
        </div>
        
        <!-- Message -->
        <div style="margin: 25px 0;">
          <h3 style="color: #374151; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">💬 Client Message</h3>
          <div style="background-color: #f9fafb; padding: 20px; border-radius: 12px; border: 1px solid #e5e7eb;">
            <p style="margin: 0; white-space: pre-wrap; font-size: 15px; line-height: 1.6;">${
              inquiry.message
            }</p>
          </div>
        </div>
        
        <!-- Action Buttons -->
        <div style="text-align: center; margin: 30px 0; padding: 20px 0; border-top: 1px solid #e5e7eb;">
          <a href="mailto:${
            inquiry.clientEmail
          }?subject=Re: Property Inquiry - ${property.title}" 
             style="display: inline-block; background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; font-weight: 600; margin: 0 10px;">
            📧 Reply to Client
          </a>
          ${
            inquiry.clientPhone
              ? `
          <a href="tel:${inquiry.clientPhone}" 
             style="display: inline-block; background-color: #059669; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; font-weight: 600; margin: 0 10px;">
            📞 Call Client
          </a>`
              : ""
          }
        </div>
      </div>
      
      <!-- Footer -->
      <div style="background-color: #f3f4f6; padding: 20px; text-align: center; color: #6b7280; font-size: 14px;">
        <p style="margin: 0 0 10px 0;">This inquiry was submitted through the UrbanEdge Real Estate website.</p>
        <p style="margin: 0;">Please respond promptly to maintain excellent customer service.</p>
      </div>
    </body>
    </html>
  `;
}

export function generateTourRequestTemplate(data: EmailTemplateData): string {
  const { inquiry, property } = data;

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Tour Request - ${property.title}</title>
    </head>
    <body style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 0; background-color: #f5f5f5;">
      
      <!-- Header -->
      <div style="background: linear-gradient(135deg, #059669 0%, #047857 100%); color: white; padding: 30px 20px; text-align: center;">
        <h1 style="margin: 0; font-size: 28px; font-weight: 700;">UrbanEdge Real Estate</h1>
        <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">🏠 New Tour Request</p>
      </div>
      
      <!-- Main Content -->
      <div style="background-color: white; padding: 30px; margin: 0;">
        
        <!-- Property Header -->
        <div style="border-bottom: 2px solid #e5e7eb; padding-bottom: 20px; margin-bottom: 25px;">
          <h2 style="color: #1f2937; margin: 0 0 10px 0; font-size: 24px; font-weight: 600;">${
            property.title
          }</h2>
          <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;">
            <p style="margin: 0; color: #6b7280; font-size: 16px;">📍 ${
              property.location
            }</p>
            <p style="margin: 0; color: #059669; font-size: 20px; font-weight: 700;">$${property.price.toLocaleString()}</p>
          </div>
        </div>
        
        <!-- Tour Details -->
        <div style="background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%); padding: 20px; border-radius: 12px; margin: 25px 0; border: 1px solid #a7f3d0;">
          <h3 style="color: #065f46; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">🗓️ Requested Tour Details</h3>
          <div style="display: grid; gap: 10px;">
            <p style="margin: 0; font-size: 16px;"><strong>Preferred Date:</strong> <span style="color: #059669;">${
              inquiry.tourDate || "Flexible"
            }</span></p>
            <p style="margin: 0; font-size: 16px;"><strong>Preferred Time:</strong> <span style="color: #059669;">${
              inquiry.tourTime || "Flexible"
            }</span></p>
          </div>
        </div>
        
        <!-- Client Information -->
        <div style="background-color: #f8fafc; padding: 20px; border-radius: 12px; margin: 25px 0; border-left: 4px solid #059669;">
          <h3 style="color: #374151; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">👤 Client Information</h3>
          <div style="display: grid; gap: 8px;">
            <p style="margin: 0; font-size: 15px;"><strong>Name:</strong> ${
              inquiry.clientName
            }</p>
            <p style="margin: 0; font-size: 15px;"><strong>Email:</strong> <a href="mailto:${
              inquiry.clientEmail
            }" style="color: #059669; text-decoration: none;">${
    inquiry.clientEmail
  }</a></p>
            ${
              inquiry.clientPhone
                ? `<p style="margin: 0; font-size: 15px;"><strong>Phone:</strong> <a href="tel:${inquiry.clientPhone}" style="color: #059669; text-decoration: none;">${inquiry.clientPhone}</a></p>`
                : ""
            }
          </div>
        </div>
        
        <!-- Message -->
        <div style="margin: 25px 0;">
          <h3 style="color: #374151; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">💬 Additional Notes</h3>
          <div style="background-color: #f9fafb; padding: 20px; border-radius: 12px; border: 1px solid #e5e7eb;">
            <p style="margin: 0; white-space: pre-wrap; font-size: 15px; line-height: 1.6;">${
              inquiry.message
            }</p>
          </div>
        </div>
        
        <!-- Action Buttons -->
        <div style="text-align: center; margin: 30px 0; padding: 20px 0; border-top: 1px solid #e5e7eb;">
          <a href="mailto:${inquiry.clientEmail}?subject=Tour Confirmation - ${
    property.title
  }&body=Hi ${inquiry.clientName},%0D%0A%0D%0AThank you for your interest in ${
    property.title
  }. I'd be happy to schedule a tour for you.%0D%0A%0D%0ABest regards,%0D%0A${
    property.agent_name
  }" 
             style="display: inline-block; background-color: #059669; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; font-weight: 600; margin: 0 10px;">
            ✅ Confirm Tour
          </a>
          ${
            inquiry.clientPhone
              ? `
          <a href="tel:${inquiry.clientPhone}" 
             style="display: inline-block; background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; font-weight: 600; margin: 0 10px;">
            📞 Call Client
          </a>`
              : ""
          }
        </div>
        
        <!-- Tour Tips -->
        <div style="background-color: #fffbeb; padding: 15px; border-radius: 8px; border: 1px solid #fbbf24; margin: 20px 0;">
          <p style="margin: 0; font-size: 14px; color: #92400e;">
            <strong>💡 Tour Tip:</strong> Confirm the appointment 24 hours in advance and prepare property highlights to share during the tour.
          </p>
        </div>
      </div>
      
      <!-- Footer -->
      <div style="background-color: #f3f4f6; padding: 20px; text-align: center; color: #6b7280; font-size: 14px;">
        <p style="margin: 0 0 10px 0;">This tour request was submitted through the UrbanEdge Real Estate website.</p>
        <p style="margin: 0;">Please respond within 24 hours to maintain excellent customer service.</p>
      </div>
    </body>
    </html>
  `;
}

export function generatePlainTextEmail(data: EmailTemplateData): string {
  const { inquiry, property } = data;

  const tourSection =
    inquiry.inquiryType === "tour"
      ? `
TOUR REQUEST DETAILS:
- Preferred Date: ${inquiry.tourDate || "Flexible"}
- Preferred Time: ${inquiry.tourTime || "Flexible"}

`
      : "";

  return `
UrbanEdge Real Estate - New ${
    inquiry.inquiryType === "tour" ? "Tour Request" : "Property Inquiry"
  }

PROPERTY: ${property.title}
Location: ${property.location}
Price: $${property.price.toLocaleString()}

${tourSection}CLIENT INFORMATION:
- Name: ${inquiry.clientName}
- Email: ${inquiry.clientEmail}
${inquiry.clientPhone ? `- Phone: ${inquiry.clientPhone}` : ""}

MESSAGE:
${inquiry.message}

---
This ${
    inquiry.inquiryType === "tour" ? "tour request" : "inquiry"
  } was submitted through the UrbanEdge Real Estate website.
Please respond promptly to maintain excellent customer service.

To reply: ${inquiry.clientEmail}
${inquiry.clientPhone ? `To call: ${inquiry.clientPhone}` : ""}
  `.trim();
}

export function getEmailSubject(inquiry: any, property: any): string {
  if (inquiry.inquiryType === "tour") {
    return `Tour Request: ${property.title} - ${inquiry.clientName}`;
  }
  return `Property Inquiry: ${property.title} - ${inquiry.clientName}`;
}

export function generatePlainTextTemplate(data: EmailTemplateData): string {
  const { inquiry, property } = data;

  const tourSection =
    inquiry.inquiryType === "tour"
      ? `
TOUR REQUEST DETAILS:
- Preferred Date: ${inquiry.tourDate || "Flexible"}
- Preferred Time: ${inquiry.tourTime || "Flexible"}

`
      : "";

  return `
UrbanEdge Real Estate - New ${
    inquiry.inquiryType === "tour" ? "Tour Request" : "Property Inquiry"
  }

PROPERTY: ${property.title}
Location: ${property.location}
Price: $${property.price.toLocaleString()}

${tourSection}CLIENT INFORMATION:
- Name: ${inquiry.clientName}
- Email: ${inquiry.clientEmail}
${inquiry.clientPhone ? `- Phone: ${inquiry.clientPhone}` : ""}

MESSAGE:
${inquiry.message}

---
This ${
    inquiry.inquiryType === "tour" ? "tour request" : "inquiry"
  } was submitted through the UrbanEdge Real Estate website.
Please respond promptly to maintain excellent customer service.

To reply: ${inquiry.clientEmail}
${inquiry.clientPhone ? `To call: ${inquiry.clientPhone}` : ""}
  `.trim();
}

export function getEmailTemplate(data: EmailTemplateData): {
  html: string;
  text: string;
  subject: string;
} {
  const { inquiry, property } = data;

  const html =
    inquiry.inquiryType === "tour"
      ? generateTourRequestTemplate(data)
      : generateContactInquiryTemplate(data);

  // For Web3Forms, we'll use the plain text template as the primary format
  const text = generatePlainTextTemplate(data);
  const subject = getEmailSubject(inquiry, property);

  return { html, text, subject };
}
