# Web3Forms Email Service Setup Guide

This guide provides detailed instructions for setting up Web3Forms as the email service for UrbanEdge property inquiries. Web3Forms is completely free and works with localhost, Vercel, Netlify, and other free hosting platforms without any domain verification.

## Why Web3Forms?

✅ **Completely Free** - No monthly limits or paid tiers  
✅ **No Domain Verification** - Works with any domain including localhost  
✅ **Free Hosting Compatible** - Perfect for Vercel, Netlify, GitHub Pages  
✅ **No Setup Complexity** - Just get an access key and start sending emails  
✅ **Multi-Agent Support** - Dynamic recipient emails for different property agents  
✅ **Reliable Delivery** - Professional email delivery infrastructure  

## Step-by-Step Setup

### 1. Create Web3Forms Account

1. **Visit Web3Forms**: Go to [web3forms.com](https://web3forms.com)
2. **Sign Up**: Click "Get Started" and create a free account
3. **Verify Email**: Check your email and verify your account
4. **No Payment Required**: The service is completely free

### 2. Create Your Form

1. **Login to Dashboard**: Access your Web3Forms dashboard
2. **Create New Form**: Click "Create New Form"
3. **Form Name**: Enter "UrbanEdge Property Inquiries"
4. **Get Access Key**: Copy the generated access key (looks like: `xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx`)

### 3. Configure Form Settings (Optional)

In your Web3Forms dashboard, you can configure:

- **Email Templates**: Customize how emails appear
- **Auto-Response**: Send confirmation emails to clients
- **Spam Protection**: Enable reCAPTCHA if needed
- **Webhooks**: Get notified of new submissions

### 4. Set Up Environment Variables

#### For Local Development

Create or update your `.env.local` file:

```env
# Supabase Configuration
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Web3Forms Configuration
WEB3FORMS_API_KEY=your_web3forms_access_key
```

#### For Supabase Edge Functions

Set the access key as a Supabase secret:

```bash
supabase secrets set WEB3FORMS_API_KEY=your_web3forms_access_key
```

### 5. Deploy the Edge Function

Use the provided deployment script:

```bash
# Set environment variable
export WEB3FORMS_API_KEY=your_web3forms_access_key

# Run deployment script
./scripts/deploy-email-service.sh
```

Or deploy manually:

```bash
# Set Supabase secret
supabase secrets set WEB3FORMS_API_KEY=your_web3forms_access_key

# Deploy function
supabase functions deploy send-property-inquiry
```

### 6. Test the Integration

#### Quick Test with cURL

```bash
curl -X POST 'https://your-project.supabase.co/functions/v1/send-property-inquiry' \
  -H 'Authorization: Bearer your_supabase_anon_key' \
  -H 'Content-Type: application/json' \
  -d '{
    "propertyId": "test-property-id",
    "inquiryType": "contact",
    "clientName": "Test User",
    "clientEmail": "<EMAIL>",
    "message": "Test inquiry message"
  }'
```

#### Run Test Suite

```bash
npm run email:test
```

## Free Hosting Platform Setup

### Vercel Deployment

1. **Environment Variables**: In Vercel dashboard, add:
   ```
   VITE_SUPABASE_URL=https://your-project.supabase.co
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

2. **Deploy**: Push to your connected Git repository
3. **Test**: The email service will work immediately with your Vercel domain

### Netlify Deployment

1. **Environment Variables**: In Netlify dashboard, add:
   ```
   VITE_SUPABASE_URL=https://your-project.supabase.co
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

2. **Deploy**: Push to your connected Git repository
3. **Test**: The email service will work with your Netlify domain

### GitHub Pages

1. **GitHub Secrets**: In repository settings, add:
   ```
   VITE_SUPABASE_URL=https://your-project.supabase.co
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

2. **Build Process**: Ensure your build process includes environment variables
3. **Deploy**: Use GitHub Actions to deploy to GitHub Pages

## Localhost Development

Web3Forms works perfectly with localhost during development:

1. **Start Development Server**:
   ```bash
   npm run dev
   ```

2. **Test Email Functionality**: Submit forms from `http://localhost:3000`
3. **Check Web3Forms Dashboard**: View submissions in real-time

## Monitoring and Analytics

### Web3Forms Dashboard

Access your dashboard to monitor:

- **Submissions**: View all form submissions
- **Success Rate**: Monitor delivery success
- **Spam Detection**: Review blocked submissions
- **Analytics**: Track submission trends

### Supabase Function Logs

Monitor your Edge Function:

```bash
# View real-time logs
supabase functions logs send-property-inquiry --follow

# View recent logs
supabase functions logs send-property-inquiry
```

## Troubleshooting

### Common Issues

#### 1. "WEB3FORMS_API_KEY not configured"

**Solution**:
```bash
# Check if secret is set
supabase secrets list

# Set the secret
supabase secrets set WEB3FORMS_API_KEY=your_access_key

# Redeploy function
supabase functions deploy send-property-inquiry
```

#### 2. Emails not being received

**Possible Causes**:
- Invalid recipient email address
- Emails going to spam folder
- Web3Forms service issues

**Solutions**:
1. Check Web3Forms dashboard for submission status
2. Verify recipient email is valid
3. Check spam/junk folders
4. Test with a different email address

#### 3. CORS errors in browser

**Solution**: Ensure your Supabase Edge Function has proper CORS headers (already configured in the provided code)

#### 4. Rate limiting issues

The built-in rate limiting (10 requests per 15 minutes) should be sufficient for most use cases. If you need higher limits, you can adjust them in the security.ts file.

## Advantages Over Other Services

### vs. Resend
- ✅ Completely free (Resend has paid tiers)
- ✅ No domain verification required
- ✅ Works with localhost immediately

### vs. EmailJS
- ✅ Server-side processing (more secure)
- ✅ Better for multi-agent scenarios
- ✅ No client-side API key exposure

### vs. SendGrid
- ✅ No complex setup or verification
- ✅ No monthly limits
- ✅ Perfect for small to medium real estate platforms

## Production Considerations

### Email Deliverability

Web3Forms uses professional email infrastructure to ensure good deliverability:

- **SPF/DKIM**: Properly configured email authentication
- **Reputation**: Good sender reputation
- **Monitoring**: Built-in spam detection

### Scaling

For high-volume real estate platforms:

- **No Limits**: Web3Forms has no monthly submission limits
- **Performance**: Fast email delivery
- **Reliability**: 99.9% uptime SLA

### Security

- **Server-Side**: All email processing happens server-side
- **Rate Limiting**: Built-in protection against abuse
- **Validation**: Comprehensive input validation

## Support and Resources

- **Web3Forms Documentation**: [docs.web3forms.com](https://docs.web3forms.com)
- **Support**: Contact Web3Forms support for service issues
- **UrbanEdge Issues**: Create GitHub issues for integration problems

## Migration from Other Services

If migrating from Resend or EmailJS:

1. **Get Web3Forms Access Key**: Follow setup steps above
2. **Update Environment Variables**: Replace old API keys
3. **Deploy Updated Function**: Use the deployment script
4. **Test Thoroughly**: Ensure all functionality works
5. **Monitor**: Check both Supabase logs and Web3Forms dashboard

The migration is seamless and requires no changes to your React components or user interface.
