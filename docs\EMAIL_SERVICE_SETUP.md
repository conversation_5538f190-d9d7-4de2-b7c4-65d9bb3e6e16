# Email Service Setup Guide

This guide explains how to set up the email delivery system for property inquiries using Supabase Edge Functions and Resend.

## Prerequisites

1. **Supabase Project**: You need an active Supabase project
2. **Resend Account**: Sign up at [resend.com](https://resend.com) for email delivery
3. **Supabase CLI**: Install the Supabase CLI for deploying Edge Functions

## Step 1: Setup Resend Email Service

### 1.1 Create Resend Account
1. Go to [resend.com](https://resend.com) and sign up
2. Verify your email address
3. Complete the onboarding process

### 1.2 Get API Key
1. In your Resend dashboard, go to "API Keys"
2. Click "Create API Key"
3. Name it "UrbanEdge Production" or similar
4. Copy the API key (starts with `re_`)

### 1.3 Configure Domain (Optional but Recommended)
1. In Resend dashboard, go to "Domains"
2. Add your domain (e.g., `urbanedge.com`)
3. Follow DNS configuration instructions
4. Verify domain ownership

## Step 2: Deploy Supabase Edge Function

### 2.1 Install Supabase CLI
```bash
# Install via npm
npm install -g supabase

# Or via homebrew (macOS)
brew install supabase/tap/supabase
```

### 2.2 Login to Supabase
```bash
supabase login
```

### 2.3 Link Your Project
```bash
# In your project root directory
supabase link --project-ref YOUR_PROJECT_REF
```

### 2.4 Set Environment Variables
```bash
# Set the Resend API key
supabase secrets set RESEND_API_KEY=your_resend_api_key_here

# Optional: Set custom from email
supabase secrets set FROM_EMAIL=<EMAIL>
supabase secrets set FROM_NAME="UrbanEdge Real Estate"
```

### 2.5 Deploy the Edge Function
```bash
# Deploy the send-property-inquiry function
supabase functions deploy send-property-inquiry
```

## Step 3: Test the Email Function

### 3.1 Test via Supabase Dashboard
1. Go to your Supabase project dashboard
2. Navigate to "Edge Functions"
3. Find "send-property-inquiry" function
4. Use the test interface with sample data:

```json
{
  "propertyId": "your-property-id",
  "inquiryType": "contact",
  "clientName": "John Doe",
  "clientEmail": "<EMAIL>",
  "clientPhone": "************",
  "message": "I'm interested in this property. Please contact me."
}
```

### 3.2 Test via cURL
```bash
curl -X POST 'https://YOUR_PROJECT_REF.supabase.co/functions/v1/send-property-inquiry' \
  -H 'Authorization: Bearer YOUR_ANON_KEY' \
  -H 'Content-Type: application/json' \
  -d '{
    "propertyId": "your-property-id",
    "inquiryType": "contact",
    "clientName": "Test User",
    "clientEmail": "<EMAIL>",
    "message": "Test inquiry message"
  }'
```

## Step 4: Frontend Integration

The PropertyContactForm component will automatically use the deployed Edge Function. Make sure your environment variables are set:

```env
# In your .env.local file
VITE_SUPABASE_URL=https://YOUR_PROJECT_REF.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key
```

## Step 5: Monitoring and Logs

### 5.1 View Function Logs
```bash
# View real-time logs
supabase functions logs send-property-inquiry --follow
```

### 5.2 Monitor in Resend Dashboard
1. Go to Resend dashboard
2. Check "Logs" section for email delivery status
3. Monitor bounce rates and delivery metrics

## Troubleshooting

### Common Issues

1. **"RESEND_API_KEY not configured" Error**
   - Ensure you've set the secret: `supabase secrets set RESEND_API_KEY=your_key`
   - Redeploy the function after setting secrets

2. **"Property not found" Error**
   - Verify the propertyId exists in your database
   - Check that the property has agent_email field populated

3. **Email not delivered**
   - Check Resend logs for delivery status
   - Verify the agent email address is valid
   - Check spam folders

4. **Rate limit exceeded**
   - The function limits 10 emails per 15 minutes per IP
   - This prevents spam and abuse

### Debug Mode
To enable detailed logging, add this to your Edge Function:
```typescript
console.log('Debug info:', { inquiry, property })
```

## Security Considerations

1. **Rate Limiting**: Built-in rate limiting prevents abuse
2. **Input Validation**: All inputs are validated before processing
3. **CORS**: Properly configured for your domain
4. **API Keys**: Stored securely as Supabase secrets
5. **Email Validation**: Client emails are validated before sending

## Production Checklist

- [ ] Resend account verified and domain configured
- [ ] API keys set as Supabase secrets
- [ ] Edge Function deployed successfully
- [ ] Test emails sent and received
- [ ] Frontend integration working
- [ ] Monitoring and logging configured
- [ ] Rate limiting tested
- [ ] Error handling verified

## Support

For issues with:
- **Supabase Edge Functions**: Check Supabase documentation
- **Resend Email Service**: Contact Resend support
- **UrbanEdge Integration**: Check application logs and Edge Function logs
