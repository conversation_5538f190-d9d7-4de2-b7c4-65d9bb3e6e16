import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import { getEmailTemplate, type EmailTemplateData } from "./email-templates.ts";
import {
  RateLimiter,
  getClientIdentifier,
  validateInquiryData,
  createSecurityHeaders,
  logSecurityEvent,
} from "./security.ts";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
  ...createSecurityHeaders(),
};

// Initialize rate limiter
const rateLimiter = new RateLimiter(15 * 60 * 1000, 10); // 10 requests per 15 minutes

interface PropertyInquiry {
  propertyId: string;
  inquiryType: "contact" | "tour";
  clientName: string;
  clientEmail: string;
  clientPhone?: string;
  message: string;
  tourDate?: string;
  tourTime?: string;
}

interface PropertyAgent {
  name: string;
  email: string;
  phone?: string;
}

interface PropertyDetails {
  id: string;
  title: string;
  location: string;
  price: number;
  agent_name: string;
  agent_email: string;
  agent_phone?: string;
}

// Rate limiting and validation moved to security.ts

async function getPropertyDetails(
  supabase: any,
  propertyId: string
): Promise<PropertyDetails | null> {
  try {
    const { data, error } = await supabase
      .from("properties")
      .select(
        "id, title, location, price, agent_name, agent_email, agent_phone"
      )
      .eq("id", propertyId)
      .single();

    if (error) {
      console.error("Error fetching property:", error);
      return null;
    }

    return data;
  } catch (error) {
    console.error("Error in getPropertyDetails:", error);
    return null;
  }
}

async function sendEmail(
  inquiry: PropertyInquiry,
  property: PropertyDetails
): Promise<boolean> {
  // FormSubmit.co doesn't require API keys - it uses the agent's email directly in the URL
  if (!property.agent_email) {
    console.error("Agent email not found for property");
    return false;
  }

  // Generate email content using templates
  const templateData: EmailTemplateData = { inquiry, property };
  const {
    html: emailHtml,
    text: emailText,
    subject: emailSubject,
  } = getEmailTemplate(templateData);

  try {
    // FormSubmit.co API expects form data
    const formData = new FormData();

    // FormSubmit.co special fields (prefixed with _)
    formData.append("_subject", emailSubject);
    formData.append("_replyto", inquiry.clientEmail); // Reply-to field
    formData.append("_template", "table"); // Use table template for better formatting
    formData.append("_captcha", "false"); // Disable captcha for API submissions

    // Client information
    formData.append("name", inquiry.clientName);
    formData.append("email", inquiry.clientEmail);
    formData.append("phone", inquiry.clientPhone || "Not provided");
    formData.append("message", inquiry.message);

    // Property details
    formData.append("property_title", property.title);
    formData.append("property_location", property.location);
    formData.append("property_price", `$${property.price.toLocaleString()}`);
    formData.append("inquiry_type", inquiry.inquiryType);

    // Tour details if applicable
    if (inquiry.inquiryType === "tour") {
      formData.append("tour_date", inquiry.tourDate || "Flexible");
      formData.append("tour_time", inquiry.tourTime || "Flexible");
    }

    // Add formatted message with all details
    const formattedMessage = `
${inquiry.message}

--- Property Details ---
Property: ${property.title}
Location: ${property.location}
Price: $${property.price.toLocaleString()}

--- Client Information ---
Name: ${inquiry.clientName}
Email: ${inquiry.clientEmail}
Phone: ${inquiry.clientPhone || "Not provided"}

${
  inquiry.inquiryType === "tour"
    ? `
--- Tour Request ---
Preferred Date: ${inquiry.tourDate || "Not specified"}
Preferred Time: ${inquiry.tourTime || "Not specified"}
`
    : ""
}

This ${
      inquiry.inquiryType === "tour" ? "tour request" : "inquiry"
    } was submitted through the UrbanEdge Real Estate website.
Please respond directly to the client's email address: ${inquiry.clientEmail}
    `.trim();

    formData.append("message", formattedMessage);

    // FormSubmit.co endpoint - uses agent email directly in URL
    const formSubmitUrl = `https://formsubmit.co/${property.agent_email}`;

    const response = await fetch(formSubmitUrl, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error("FormSubmit.co API error:", response.status, errorData);
      return false;
    }

    // FormSubmit.co returns HTML by default, but we can check the status
    const responseText = await response.text();

    // If we get here and response was ok, the email was sent successfully
    console.log(
      "Email sent successfully via FormSubmit.co to:",
      property.agent_email
    );
    return true;
  } catch (error) {
    console.error("Error sending email:", error);
    return false;
  }
}

// Email template functions moved to email-templates.ts

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    // Get client identifier for rate limiting
    const clientId = getClientIdentifier(req);

    // Check rate limit
    const rateLimit = rateLimiter.checkLimit(clientId);
    if (!rateLimit.allowed) {
      logSecurityEvent("RATE_LIMIT_EXCEEDED", { clientId }, req);
      return new Response(
        JSON.stringify({
          error: "Rate limit exceeded. Please try again later.",
          resetTime: rateLimit.resetTime,
        }),
        {
          status: 429,
          headers: {
            ...corsHeaders,
            "Content-Type": "application/json",
            "X-RateLimit-Remaining": "0",
            "X-RateLimit-Reset": rateLimit.resetTime.toString(),
          },
        }
      );
    }

    // Parse and validate request body
    const rawData = await req.json();
    const validation = validateInquiryData(rawData);

    if (!validation.isValid) {
      logSecurityEvent(
        "INVALID_INPUT",
        { errors: validation.errors, clientId },
        req
      );
      return new Response(
        JSON.stringify({
          error: "Invalid inquiry data",
          details: validation.errors,
        }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    const inquiry = validation.sanitizedData as PropertyInquiry;

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get("SUPABASE_URL")!;
    const supabaseKey = Deno.env.get("SUPABASE_ANON_KEY")!;
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Get property details
    const property = await getPropertyDetails(supabase, inquiry.propertyId);
    if (!property) {
      return new Response(JSON.stringify({ error: "Property not found" }), {
        status: 404,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    // Send email
    const emailSent = await sendEmail(inquiry, property);
    if (!emailSent) {
      logSecurityEvent(
        "EMAIL_SEND_FAILED",
        {
          propertyId: inquiry.propertyId,
          clientId,
        },
        req
      );
      return new Response(
        JSON.stringify({
          error: "Failed to send email. Please try again later.",
        }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Log successful email send
    console.log(
      `Email sent successfully for property ${inquiry.propertyId} to ${property.agent_email}`
    );

    // Return success response with rate limit headers
    return new Response(
      JSON.stringify({
        success: true,
        message: "Inquiry sent successfully",
        agentName: property.agent_name,
      }),
      {
        status: 200,
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json",
          "X-RateLimit-Remaining": rateLimit.remaining.toString(),
          "X-RateLimit-Reset": rateLimit.resetTime.toString(),
        },
      }
    );
  } catch (error) {
    const clientId = getClientIdentifier(req);
    logSecurityEvent(
      "UNEXPECTED_ERROR",
      {
        error: error.message,
        stack: error.stack,
        clientId,
      },
      req
    );
    console.error("Edge function error:", error);

    return new Response(
      JSON.stringify({
        error: "Internal server error. Please try again later.",
      }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );
  }
});
