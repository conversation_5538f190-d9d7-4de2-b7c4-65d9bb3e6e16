#!/usr/bin/env node

/**
 * UrbanEdge Email Service Setup Validation Script
 * 
 * This script validates that the email service is properly configured
 * and ready for production use.
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

// Configuration
const config = {
  supabaseUrl: process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL,
  supabaseKey: process.env.VITE_SUPABASE_ANON_KEY || process.env.SUPABASE_ANON_KEY,
  resendApiKey: process.env.RESEND_API_KEY,
};

// Validation results
const results = {
  passed: 0,
  failed: 0,
  warnings: 0,
  checks: []
};

function addCheck(name, status, message, type = 'info') {
  results.checks.push({ name, status, message, type });
  if (status === 'PASS') results.passed++;
  else if (status === 'FAIL') results.failed++;
  else if (status === 'WARN') results.warnings++;
}

function makeRequest(url, options) {
  return new Promise((resolve, reject) => {
    const req = https.request(url, options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(body);
          resolve({ status: res.statusCode, headers: res.headers, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, headers: res.headers, data: body });
        }
      });
    });
    req.on('error', reject);
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    req.end();
  });
}

async function validateEnvironment() {
  console.log('🔍 Validating Environment Configuration');
  console.log('=====================================\n');

  // Check Supabase URL
  if (config.supabaseUrl) {
    if (config.supabaseUrl.includes('supabase.co')) {
      addCheck('Supabase URL', 'PASS', `Valid URL: ${config.supabaseUrl}`);
    } else {
      addCheck('Supabase URL', 'WARN', 'URL format may be incorrect');
    }
  } else {
    addCheck('Supabase URL', 'FAIL', 'VITE_SUPABASE_URL or SUPABASE_URL not set');
  }

  // Check Supabase Key
  if (config.supabaseKey) {
    if (config.supabaseKey.length > 100) {
      addCheck('Supabase Anon Key', 'PASS', 'Key appears to be valid format');
    } else {
      addCheck('Supabase Anon Key', 'WARN', 'Key may be too short');
    }
  } else {
    addCheck('Supabase Anon Key', 'FAIL', 'VITE_SUPABASE_ANON_KEY or SUPABASE_ANON_KEY not set');
  }

  // Check Resend API Key (optional for this validation)
  if (config.resendApiKey) {
    if (config.resendApiKey.startsWith('re_')) {
      addCheck('Resend API Key', 'PASS', 'Key format is correct');
    } else {
      addCheck('Resend API Key', 'WARN', 'Key format may be incorrect (should start with re_)');
    }
  } else {
    addCheck('Resend API Key', 'WARN', 'RESEND_API_KEY not set in environment (should be set as Supabase secret)');
  }
}

async function validateFiles() {
  console.log('\n📁 Validating File Structure');
  console.log('============================\n');

  const requiredFiles = [
    'supabase/functions/send-property-inquiry/index.ts',
    'supabase/functions/send-property-inquiry/email-templates.ts',
    'supabase/functions/send-property-inquiry/security.ts',
    'src/components/Properties/PropertyDetail/PropertyContactForm.jsx',
    'docs/EMAIL_SERVICE_SETUP.md',
    'docs/EMAIL_SERVICE_API.md'
  ];

  for (const file of requiredFiles) {
    if (fs.existsSync(file)) {
      addCheck(`File: ${file}`, 'PASS', 'File exists');
    } else {
      addCheck(`File: ${file}`, 'FAIL', 'File missing');
    }
  }

  // Check if deployment script is executable
  const deployScript = 'scripts/deploy-email-service.sh';
  if (fs.existsSync(deployScript)) {
    try {
      const stats = fs.statSync(deployScript);
      if (stats.mode & parseInt('111', 8)) {
        addCheck('Deploy Script', 'PASS', 'Script is executable');
      } else {
        addCheck('Deploy Script', 'WARN', 'Script exists but may not be executable (run: chmod +x scripts/deploy-email-service.sh)');
      }
    } catch (error) {
      addCheck('Deploy Script', 'WARN', 'Could not check script permissions');
    }
  } else {
    addCheck('Deploy Script', 'FAIL', 'Deployment script missing');
  }
}

async function validateSupabaseConnection() {
  console.log('\n🔗 Validating Supabase Connection');
  console.log('=================================\n');

  if (!config.supabaseUrl || !config.supabaseKey) {
    addCheck('Supabase Connection', 'FAIL', 'Missing URL or key');
    return;
  }

  try {
    // Test basic Supabase connection
    const response = await makeRequest(`${config.supabaseUrl}/rest/v1/`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${config.supabaseKey}`,
        'apikey': config.supabaseKey
      }
    });

    if (response.status === 200) {
      addCheck('Supabase REST API', 'PASS', 'Connection successful');
    } else {
      addCheck('Supabase REST API', 'FAIL', `Connection failed with status ${response.status}`);
    }
  } catch (error) {
    addCheck('Supabase REST API', 'FAIL', `Connection error: ${error.message}`);
  }
}

async function validateEdgeFunction() {
  console.log('\n⚡ Validating Edge Function');
  console.log('===========================\n');

  if (!config.supabaseUrl || !config.supabaseKey) {
    addCheck('Edge Function', 'FAIL', 'Missing Supabase configuration');
    return;
  }

  const functionUrl = `${config.supabaseUrl}/functions/v1/send-property-inquiry`;

  try {
    // Test OPTIONS request (CORS preflight)
    const optionsResponse = await makeRequest(functionUrl, {
      method: 'OPTIONS',
      headers: {
        'Authorization': `Bearer ${config.supabaseKey}`,
        'Origin': 'http://localhost:3000'
      }
    });

    if (optionsResponse.status === 200) {
      addCheck('Edge Function CORS', 'PASS', 'CORS preflight successful');
    } else {
      addCheck('Edge Function CORS', 'WARN', `CORS preflight returned ${optionsResponse.status}`);
    }

    // Test invalid request (should return 400)
    const invalidResponse = await makeRequest(functionUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.supabaseKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (invalidResponse.status === 400) {
      addCheck('Edge Function Validation', 'PASS', 'Function properly validates input');
    } else if (invalidResponse.status === 404) {
      addCheck('Edge Function Deployment', 'FAIL', 'Function not found - may not be deployed');
    } else {
      addCheck('Edge Function Response', 'WARN', `Unexpected response: ${invalidResponse.status}`);
    }

  } catch (error) {
    if (error.message.includes('timeout')) {
      addCheck('Edge Function', 'FAIL', 'Function timeout - may not be deployed or responding');
    } else {
      addCheck('Edge Function', 'FAIL', `Function error: ${error.message}`);
    }
  }
}

async function validateDatabase() {
  console.log('\n🗄️  Validating Database Schema');
  console.log('==============================\n');

  if (!config.supabaseUrl || !config.supabaseKey) {
    addCheck('Database Schema', 'FAIL', 'Missing Supabase configuration');
    return;
  }

  try {
    // Check if properties table has agent fields
    const response = await makeRequest(`${config.supabaseUrl}/rest/v1/properties?select=agent_name,agent_email,agent_phone,agent_whatsapp_link&limit=1`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${config.supabaseKey}`,
        'apikey': config.supabaseKey
      }
    });

    if (response.status === 200) {
      addCheck('Properties Table Schema', 'PASS', 'Agent contact fields are available');
    } else if (response.status === 400 && response.data.message?.includes('column')) {
      addCheck('Properties Table Schema', 'FAIL', 'Agent contact fields missing - run database migrations');
    } else {
      addCheck('Properties Table Schema', 'WARN', `Unexpected response: ${response.status}`);
    }
  } catch (error) {
    addCheck('Database Schema', 'FAIL', `Database error: ${error.message}`);
  }
}

async function generateReport() {
  console.log('\n📊 Validation Report');
  console.log('===================\n');

  // Group checks by status
  const passed = results.checks.filter(c => c.status === 'PASS');
  const failed = results.checks.filter(c => c.status === 'FAIL');
  const warnings = results.checks.filter(c => c.status === 'WARN');

  console.log(`✅ Passed: ${passed.length}`);
  console.log(`❌ Failed: ${failed.length}`);
  console.log(`⚠️  Warnings: ${warnings.length}`);
  console.log(`📋 Total Checks: ${results.checks.length}\n`);

  if (failed.length > 0) {
    console.log('❌ Failed Checks:');
    failed.forEach(check => {
      console.log(`   • ${check.name}: ${check.message}`);
    });
    console.log();
  }

  if (warnings.length > 0) {
    console.log('⚠️  Warnings:');
    warnings.forEach(check => {
      console.log(`   • ${check.name}: ${check.message}`);
    });
    console.log();
  }

  // Overall status
  if (failed.length === 0) {
    if (warnings.length === 0) {
      console.log('🎉 All checks passed! Email service is ready for production.');
    } else {
      console.log('✅ Setup is functional with some warnings. Review warnings above.');
    }
  } else {
    console.log('❌ Setup has critical issues. Please fix failed checks before proceeding.');
  }

  console.log('\n📚 Next Steps:');
  if (failed.length > 0) {
    console.log('1. Fix failed checks listed above');
    console.log('2. Re-run validation: node scripts/validate-email-setup.js');
  } else {
    console.log('1. Run test suite: node scripts/test-email-service.js');
    console.log('2. Deploy to production: ./scripts/deploy-email-service.sh');
    console.log('3. Monitor function logs: supabase functions logs send-property-inquiry --follow');
  }

  return failed.length === 0;
}

async function main() {
  console.log('🔍 UrbanEdge Email Service Setup Validation');
  console.log('==========================================\n');

  await validateEnvironment();
  await validateFiles();
  await validateSupabaseConnection();
  await validateEdgeFunction();
  await validateDatabase();
  
  const success = await generateReport();
  process.exit(success ? 0 : 1);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main };
